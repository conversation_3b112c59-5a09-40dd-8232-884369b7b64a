/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { modPanelButton } from '#src/interactions/ShowModPanel.js';
import { findOriginalMessage, getBroadcast } from '#src/utils/network/messageUtils.js';
import { HubService } from '#src/services/HubService.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import { CustomID } from '#utils/CustomID.js';
import db from '#utils/Db.js';
import { resolveEval } from '#utils/Utils.js';
import { stripIndents } from 'common-tags';
import {
  ButtonBuilder,
  ButtonStyle,
  type Client,
  type GuildTextBasedChannel,
  type User,
  codeBlock,
  messageLink,
  ContainerBuilder,
  TextDisplayBuilder,
  SectionBuilder,
  MediaGalleryBuilder,
  MediaGalleryItemBuilder,
  SeparatorBuilder,
  SeparatorSpacingSize,
  MessageFlags,
} from 'discord.js';
import { sendLog } from './Default.js';

export type ReportEvidenceOpts = {
  // the message content
  content?: string;
  messageId?: string;
  attachmentUrl?: string;
};

export type LogReportOpts = {
  userId: string;
  serverId: string;
  reason: string;
  reportedBy: User;
  evidence?: ReportEvidenceOpts;
};

/**
 * Generate a jump link to the reported message in the reports server
 */
const genJumpLink = async (
  hubId: string,
  client: Client,
  messageId?: string,
  reportsChannelId?: string,
): Promise<string | null> => {
  if (!messageId || !reportsChannelId) return null;

  const originalMsg = await findOriginalMessage(messageId);
  if (!originalMsg) return null;

  // fetch the reports server ID from the log channel's ID
  const reportsServerId = resolveEval(
    await client.cluster.broadcastEval(
      async (cl, channelId) => {
        const channel = (await cl.channels
          .fetch(channelId)
          .catch(() => null)) as GuildTextBasedChannel | null;
        return channel?.guild.id;
      },
      { context: reportsChannelId },
    ),
  );

  const networkChannel = await db.connection.findFirst({
    where: { serverId: reportsServerId, hubId },
  });

  if (!networkChannel) return null;

  const reportsServerMsg = await getBroadcast(originalMsg.id, {
    channelId: networkChannel.channelId,
  });
  if (!reportsServerMsg) return null;

  return messageLink(networkChannel.channelId, reportsServerMsg.messageId, networkChannel.serverId);
};

/**
 * Create a report record in the database
 */
const createReportRecord = async (
  hubId: string,
  { userId, serverId, reason, reportedBy, evidence }: LogReportOpts,
): Promise<string> => {
  const report = await db.report.create({
    data: {
      hubId,
      reporterId: reportedBy.id,
      reportedUserId: userId,
      reportedServerId: serverId,
      messageId: evidence?.messageId,
      reason,
      evidence: evidence ? {
        content: evidence.content,
        attachmentUrl: evidence.attachmentUrl,
      } : undefined,
    },
  });

  return report.id;
};

/**
 * Get reply context for a message if it's a reply
 */
const getReplyContext = async (messageId?: string): Promise<{
  isReply: boolean;
  originalContent?: string;
  originalAuthor?: string;
} | null> => {
  if (!messageId) return null;

  const message = await db.message.findUnique({
    where: { id: messageId },
    include: {
      referredTo: true,
    },
  });

  if (!message?.referredTo) {
    return { isReply: false };
  }

  return {
    isReply: true,
    originalContent: message.referredTo.content,
    originalAuthor: message.referredTo.authorId,
  };
};

/**
 * Enhanced report logging with Components v2 UI
 */
export const sendHubReport = async (
  hubId: string,
  client: Client,
  opts: LogReportOpts,
): Promise<void> => {
  const hub = await new HubService().fetchHub(hubId);
  const logConfig = await hub?.fetchLogConfig();

  if (!logConfig?.config.reportsChannelId || !opts.evidence?.messageId) return;

  const reportsChannelId = logConfig.config.reportsChannelId;
  const reportsRoleId = logConfig.config.reportsRoleId;
  const user = await client.users.fetch(opts.userId).catch(() => null);
  const server = await client.fetchGuild(opts.serverId);
  const jumpLink = await genJumpLink(hubId, client, opts.evidence?.messageId, reportsChannelId);

  // Create report record and get ID
  const reportId = await createReportRecord(hubId, opts);

  // Get reply context
  const replyContext = await getReplyContext(opts.evidence?.messageId);

  // Create Components v2 container
  const container = new ContainerBuilder();

  // Header section with report info
  const headerSection = new SectionBuilder().addTextDisplayComponents(
    new TextDisplayBuilder().setContent(
      stripIndents`
        # ${getEmoji('alert_icon', client)} New Report • ID: \`${reportId}\`

        **Reported User:** @${user?.username} (\`${opts.userId}\`)
        **Reported Server:** ${server?.name} (\`${opts.serverId}\`)
        **Reported by:** ${opts.reportedBy.username}
        **Reason:** ${opts.reason}
      `,
    ),
  );

  container.addSectionComponents(headerSection);

  // Add separator
  container.addSeparatorComponents(
    new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
  );

  // Reply context section (if applicable)
  if (replyContext?.isReply && replyContext.originalContent) {
    const replySection = new SectionBuilder().addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        stripIndents`
          ### ${getEmoji('reply', client)} Reply Context
          **Original message:** ${codeBlock(replyContext.originalContent.slice(0, 200))}
          **Original author:** <@${replyContext.originalAuthor}>
        `,
      ),
    );

    container.addSectionComponents(replySection);
    container.addSeparatorComponents(
      new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small),
    );
  }

  // Message content section
  const contentSection = new SectionBuilder().addTextDisplayComponents(
    new TextDisplayBuilder().setContent(
      stripIndents`
        ### ${getEmoji('info', client)} Reported Message
        ${codeBlock(opts.evidence?.content?.replaceAll('`', '\\`') || 'No content provided.')}
        **Message ID:** \`${opts.evidence.messageId}\`
      `,
    ),
  );

  container.addSectionComponents(contentSection);

  // Add image if present
  if (opts.evidence?.attachmentUrl) {
    const mediaGallery = new MediaGalleryBuilder();
    const mediaItem = new MediaGalleryItemBuilder()
      .setURL(opts.evidence.attachmentUrl)
      .setDescription('Reported message attachment');

    mediaGallery.addItems(mediaItem);
    container.addMediaGalleryComponents(mediaGallery);
  }

  // Action buttons with report ID
  const modActionButton = modPanelButton(
    opts.evidence.messageId,
    getEmoji('hammer_icon', client),
    { reportId },
  ).setLabel('Take Action');

  const resolveButton = new ButtonBuilder()
    .setCustomId(new CustomID().setIdentifier('reportAction', 'resolve').setArgs(reportId).toString())
    .setStyle(ButtonStyle.Success)
    .setLabel('Mark Resolved')
    .setEmoji(getEmoji('tick_icon', client));

  const ignoreButton = new ButtonBuilder()
    .setCustomId(new CustomID().setIdentifier('reportAction', 'ignore').setArgs(reportId).toString())
    .setStyle(ButtonStyle.Secondary)
    .setLabel('Ignore Report')
    .setEmoji(getEmoji('x_icon', client));

  const escalateButton = new ButtonBuilder()
    .setCustomId(new CustomID().setIdentifier('reportAction', 'escalate').setArgs(reportId).toString())
    .setStyle(ButtonStyle.Danger)
    .setLabel('Escalate')
    .setEmoji(getEmoji('alert_icon', client));

  // Add action buttons
  container.addActionRowComponents((row) =>
    row.addComponents(modActionButton, resolveButton, ignoreButton, escalateButton),
  );

  // Add jump link button if available
  if (jumpLink) {
    container.addActionRowComponents((row) =>
      row.addComponents(
        new ButtonBuilder()
          .setURL(jumpLink)
          .setLabel('Jump To Message')
          .setStyle(ButtonStyle.Link)
          .setEmoji(getEmoji('link', client)),
      ),
    );
  }

  // Send the log with Components v2
  await sendLog(client.cluster, reportsChannelId, null, {
    roleMentionIds: reportsRoleId ? [reportsRoleId] : undefined,
    components: [container],
    flags: [MessageFlags.IsComponentsV2],
  });
};
