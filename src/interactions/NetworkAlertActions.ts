/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { RegisterInteractionHandler } from '#main';
import type { ComponentContext } from '#src/core/CommandContext/ComponentContext.js';
import { findOriginalMessage } from '#src/utils/network/messageUtils.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import { handleError } from '#src/utils/Utils.js';
import { stripIndents } from 'common-tags';
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  type MessageActionRowComponentBuilder,
  EmbedBuilder,
  Colors,
  codeBlock,
} from 'discord.js';

export default class NetworkAlertActionsHandler {
  /**
   * Updates button components in a message
   */
  private async updateButtonComponents(
    ctx: ComponentContext,
    actionLabel: string,
  ): Promise<void> {
    await ctx.deferUpdate();
    const components = ctx.interaction.message?.components;
    if (!components) return;

    const rows = components.map((row) =>
      // @ts-expect-error fix the types for components v2 compatibility
      ActionRowBuilder.from(row),
    ) as ActionRowBuilder<MessageActionRowComponentBuilder>[];

    // Update the specific button that was clicked
    for (const row of rows) {
      for (const component of row.components) {
        if (component instanceof ButtonBuilder) {
          const customId = component.data.custom_id;
          if (customId === ctx.interaction.customId) {
            component
              .setLabel(`${actionLabel} by @${ctx.user.username}`)
              .setDisabled(true)
              .setStyle(ButtonStyle.Secondary);
          }
          // Disable other alert action buttons (but keep Take Action button enabled)
          else if (customId?.includes('networkAlert:') && !customId.includes('modPanel')) {
            component.setDisabled(true);
          }
        }
      }
    }

    await ctx.editReply({ components: rows });
  }

  @RegisterInteractionHandler('networkAlert', 'viewMessage')
  async handleViewMessage(ctx: ComponentContext): Promise<void> {
    const [messageId, channelId] = ctx.customId.args;

    try {
      await ctx.deferReply({ flags: ['Ephemeral'] });

      // Find the original message
      const originalMessage = await findOriginalMessage(messageId);
      
      if (!originalMessage) {
        const embed = new EmbedBuilder()
          .setTitle(`${getEmoji('x_icon', ctx.client)} Message Not Found`)
          .setDescription('The original message could not be found. It may have been deleted.')
          .setColor(Colors.Red);

        await ctx.editReply({ embeds: [embed] });
        return;
      }

      // Get the author
      const author = await ctx.client.users.fetch(originalMessage.authorId).catch(() => null);
      const server = await ctx.client.fetchGuild(originalMessage.guildId).catch(() => null);

      const embed = new EmbedBuilder()
        .setTitle(`${getEmoji('search', ctx.client)} Message Details`)
        .setDescription(
          stripIndents`
            **Author:** ${author?.username || 'Unknown'} (\`${originalMessage.authorId}\`)
            **Server:** ${server?.name || 'Unknown'} (\`${originalMessage.guildId}\`)
            **Channel:** <#${channelId}>
            **Message ID:** \`${messageId}\`
            **Sent:** <t:${Math.floor(originalMessage.createdAt.getTime() / 1000)}:F>
            
            **Content:**
            ${codeBlock(originalMessage.content || 'No content')}
          `,
        )
        .setColor(Colors.Blue)
        .setTimestamp();

      if (author) {
        embed.setThumbnail(author.displayAvatarURL());
      }

      await ctx.editReply({ embeds: [embed] });
    }
    catch (error) {
      handleError(error, { 
        repliable: ctx.interaction, 
        comment: `Failed to view message ${messageId}` 
      });
    }
  }

  @RegisterInteractionHandler('networkAlert', 'dismiss')
  async handleDismissAlert(ctx: ComponentContext): Promise<void> {
    const [messageId] = ctx.customId.args;

    try {
      // Update UI to show dismissed state
      await this.updateButtonComponents(ctx, 'Dismissed');

      // Optionally log the dismissal
      console.log(`Network alert for message ${messageId} dismissed by ${ctx.user.username} (${ctx.user.id})`);
    }
    catch (error) {
      handleError(error, { 
        repliable: ctx.interaction, 
        comment: `Failed to dismiss alert for message ${messageId}` 
      });
    }
  }
}
