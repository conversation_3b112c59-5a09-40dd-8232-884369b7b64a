/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import ComponentContext from '#src/core/CommandContext/ComponentContext.js';
import { RegisterInteractionHandler } from '#src/decorators/RegisterInteractionHandler.js';
import { ReportStatus } from '#src/generated/prisma/client/client.js';
import { ReportService } from '#src/services/ReportService.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import { handleError } from '#src/utils/Utils.js';
import db from '#utils/Db.js';
import { ActionRow, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';

export default class ReportActionV2Handler {
  /**
   * Updates button components to show only a single disabled button with the action taken
   */
  private async updateButtonComponents(
    ctx: ComponentContext,
    _reportId: string,
    newStatus: ReportStatus,
    actionLabel: string,
  ): Promise<void> {
    await ctx.deferUpdate();
    const components = ctx.interaction.message?.components;
    if (!components) return;

    const updatedComponents = [];

    // Process each row
    for (const row of components) {
      if (!(row instanceof ActionRow)) {
        updatedComponents.push(row);
        continue;
      }

      let hasReportAction = false;

      // Check if this row contains report action buttons
      for (const component of row.components) {
        if (component instanceof ButtonBuilder && 'custom_id' in component.data) {
          const customId = component.data.custom_id;
          if (customId?.includes('reportAction:')) {
            hasReportAction = true;
            break;
          }
        }
      }

      // If this row has report action buttons, replace them with a single disabled button
      if (hasReportAction) {
        const newRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
          new ButtonBuilder()
            .setCustomId('report_action_completed')
            .setLabel(`${actionLabel} by @${ctx.user.username}`)
            .setDisabled(true)
            .setStyle(ButtonStyle.Secondary)
            .setEmoji(
              newStatus === ReportStatus.RESOLVED
                ? getEmoji('tick_icon', ctx.client)
                : getEmoji('x_icon', ctx.client),
            ),
        );
        updatedComponents.push(newRow);
      }
      else {
        // Keep other rows unchanged
        updatedComponents.push(row);
      }
    }

    await ctx.editReply({ components: updatedComponents });
  }

  /**
   * Send DM notification to the reporter using ReportService
   */
  private async notifyReporter(
    ctx: ComponentContext,
    reportId: string,
    status: ReportStatus,
  ): Promise<void> {
    await ReportService.sendReporterNotification(ctx.client, reportId, status);
  }

  @RegisterInteractionHandler('reportAction', 'resolve')
  async handleResolveReport(ctx: ComponentContext): Promise<void> {
    const [reportId] = ctx.customId.args;

    try {
      // Update report status in database
      await db.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.RESOLVED,
          handledBy: ctx.user.id,
          handledAt: new Date(),
        },
      });

      // Update UI
      await this.updateButtonComponents(ctx, reportId, ReportStatus.RESOLVED, 'Resolved');

      // Notify reporter
      await this.notifyReporter(ctx, reportId, ReportStatus.RESOLVED);
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: `Failed to resolve report ${reportId}`,
      });
    }
  }

  @RegisterInteractionHandler('reportAction', 'ignore')
  async handleIgnoreReport(ctx: ComponentContext): Promise<void> {
    const [reportId] = ctx.customId.args;

    try {
      // Update report status in database
      await db.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.IGNORED,
          handledBy: ctx.user.id,
          handledAt: new Date(),
        },
      });

      // Update UI
      await this.updateButtonComponents(ctx, reportId, ReportStatus.IGNORED, 'Ignored');

      // Notify reporter
      await this.notifyReporter(ctx, reportId, ReportStatus.IGNORED);
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: `Failed to ignore report ${reportId}`,
      });
    }
  }

  @RegisterInteractionHandler('reportAction', 'escalate')
  async handleEscalateReport(ctx: ComponentContext): Promise<void> {
    const [reportId] = ctx.customId.args;

    try {
      // Update report status in database
      await db.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.ESCALATED,
          handledBy: ctx.user.id,
          handledAt: new Date(),
        },
      });

      // Update UI
      await this.updateButtonComponents(ctx, reportId, ReportStatus.ESCALATED, 'Escalated');

      // Notify reporter
      await this.notifyReporter(ctx, reportId, ReportStatus.ESCALATED);
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: `Failed to escalate report ${reportId}`,
      });
    }
  }
}
