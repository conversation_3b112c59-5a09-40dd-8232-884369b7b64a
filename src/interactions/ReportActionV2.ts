/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { RegisterInteractionHandler } from '#main';
import type { ComponentContext } from '#src/core/CommandContext/ComponentContext.js';
import { ReportStatus } from '#src/generated/prisma/client/client.js';
import { ReportService } from '#src/services/ReportService.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import { handleError } from '#src/utils/Utils.js';
import db from '#utils/Db.js';
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  type MessageActionRowComponentBuilder,
} from 'discord.js';

export default class ReportActionV2Handler {
  /**
   * Updates button components in a message
   */
  private async updateButtonComponents(
    ctx: ComponentContext,
    reportId: string,
    newStatus: ReportStatus,
    actionLabel: string,
  ): Promise<void> {
    await ctx.deferUpdate();
    const components = ctx.interaction.message?.components;
    if (!components) return;

    const rows = components.map((row) =>
      // @ts-expect-error fix the types for components v2 compatibility
      ActionRowBuilder.from(row),
    ) as ActionRowBuilder<MessageActionRowComponentBuilder>[];

    // Update the specific button that was clicked
    for (const row of rows) {
      for (const component of row.components) {
        if (component instanceof ButtonBuilder) {
          const customId = component.data.custom_id;
          if (customId === ctx.interaction.customId) {
            component
              .setLabel(`${actionLabel} by @${ctx.user.username}`)
              .setDisabled(true)
              .setStyle(ButtonStyle.Secondary);
          }
          // Disable other action buttons
          else if (customId?.includes('reportAction:')) {
            component.setDisabled(true);
          }
        }
      }
    }

    await ctx.editReply({ components: rows });
  }

  /**
   * Send DM notification to the reporter using ReportService
   */
  private async notifyReporter(
    ctx: ComponentContext,
    reportId: string,
    status: ReportStatus,
    handlerUsername: string
  ): Promise<void> {
    await ReportService.sendReporterNotification(
      ctx.client,
      reportId,
      status,
      handlerUsername,
    );
  }

  @RegisterInteractionHandler('reportAction', 'resolve')
  async handleResolveReport(ctx: ComponentContext): Promise<void> {
    const [reportId] = ctx.customId.args;

    try {
      // Update report status in database
      await db.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.RESOLVED,
          handledBy: ctx.user.id,
          handledAt: new Date(),
        },
      });

      // Update UI
      await this.updateButtonComponents(ctx, reportId, ReportStatus.RESOLVED, 'Resolved');

      // Notify reporter
      await this.notifyReporter(ctx, reportId, ReportStatus.RESOLVED, ctx.user.username);
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: `Failed to resolve report ${reportId}`
      });
    }
  }

  @RegisterInteractionHandler('reportAction', 'ignore')
  async handleIgnoreReport(ctx: ComponentContext): Promise<void> {
    const [reportId] = ctx.customId.args;

    try {
      // Update report status in database
      await db.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.IGNORED,
          handledBy: ctx.user.id,
          handledAt: new Date(),
        },
      });

      // Update UI
      await this.updateButtonComponents(ctx, reportId, ReportStatus.IGNORED, 'Ignored');

      // Notify reporter
      await this.notifyReporter(ctx, reportId, ReportStatus.IGNORED, ctx.user.username);
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: `Failed to ignore report ${reportId}`
      });
    }
  }

  @RegisterInteractionHandler('reportAction', 'escalate')
  async handleEscalateReport(ctx: ComponentContext): Promise<void> {
    const [reportId] = ctx.customId.args;

    try {
      // Update report status in database
      await db.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.ESCALATED,
          handledBy: ctx.user.id,
          handledAt: new Date(),
        },
      });

      // Update UI
      await this.updateButtonComponents(ctx, reportId, ReportStatus.ESCALATED, 'Escalated');

      // Notify reporter
      await this.notifyReporter(ctx, reportId, ReportStatus.ESCALATED, ctx.user.username);
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: `Failed to escalate report ${reportId}`
      });
    }
  }
}
