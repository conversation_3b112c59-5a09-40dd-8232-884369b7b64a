/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import ComponentContext from '#src/core/CommandContext/ComponentContext.js';
import { RegisterInteractionHandler } from '#src/decorators/RegisterInteractionHandler.js';
import { ReportStatus } from '#src/generated/prisma/client/client.js';
import { ReportService } from '#src/services/ReportService.js';
import { handleError } from '#src/utils/Utils.js';
import db from '#utils/Db.js';
import { ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';

export default class ReportActionV2Handler {
  /**
   * Updates button components to show only a single disabled button with the action taken
   */
  private async updateButtonComponents(
    ctx: ComponentContext,
    _reportId: string,
    newStatus: ReportStatus,
    actionLabel: string,
  ): Promise<void> {
    await ctx.deferUpdate();
    const message = ctx.interaction.message;
    if (!message?.components) return;

    // Convert message to JSON to access raw component data
    const updatedComponents: ActionRowBuilder<ButtonBuilder>[] = [];

    // Process each row
    for (const row of message.components || []) {
      if (row.type !== ) continue; // Skip non-ActionRow components

      let hasReportAction = false;

      // Check if this row contains report action buttons
      for (const component of row.components || []) {
        if (component.type === 2 && component.customId?.includes('reportAction:')) {
          hasReportAction = true;
          break;
        }
      }

      if (hasReportAction) {
        // Replace with single disabled button
        const statusEmoji =
          newStatus === ReportStatus.RESOLVED
            ? '✅'
            : newStatus === ReportStatus.IGNORED
              ? '❌'
              : '⚠️';

        const completedButton = new ButtonBuilder()
          .setCustomId('report_action_completed')
          .setLabel(`${statusEmoji} ${actionLabel} by @${ctx.user.username}`)
          .setDisabled(true)
          .setStyle(ButtonStyle.Secondary);

        const newRow = new ActionRowBuilder<ButtonBuilder>().addComponents(completedButton);
        updatedComponents.push(newRow);
      }
      else {
        // Keep other rows unchanged
        const newRow = new ActionRowBuilder<ButtonBuilder>();

        for (const component of row.components || []) {
          if (component.type === 2) {
            // Button component
            const button = new ButtonBuilder()
              .setCustomId(component.customId || 'unknown')
              .setLabel(component.label || 'Button')
              .setStyle(component.style || ButtonStyle.Secondary);

            if (component.disabled) button.setDisabled(true);
            if (component.emoji) button.setEmoji(component.emoji);
            if (component.url) button.setURL(component.url);

            newRow.addComponents(button);
          }
        }

        if (newRow.components.length > 0) {
          updatedComponents.push(newRow);
        }
      }
    }

    console.log(updatedComponents);
    await ctx.editReply({ components: updatedComponents });
  }

  /**
   * Send DM notification to the reporter using ReportService
   */
  private async notifyReporter(
    ctx: ComponentContext,
    reportId: string,
    status: ReportStatus,
  ): Promise<void> {
    await ReportService.sendReporterNotification(ctx.client, reportId, status);
  }

  @RegisterInteractionHandler('reportAction', 'resolve')
  async handleResolveReport(ctx: ComponentContext): Promise<void> {
    const [reportId] = ctx.customId.args;

    try {
      // Update report status in database
      await db.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.RESOLVED,
          handledBy: ctx.user.id,
          handledAt: new Date(),
        },
      });

      // Update UI
      await this.updateButtonComponents(ctx, reportId, ReportStatus.RESOLVED, 'Resolved');

      // Notify reporter
      await this.notifyReporter(ctx, reportId, ReportStatus.RESOLVED);
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: `Failed to resolve report ${reportId}`,
      });
    }
  }

  @RegisterInteractionHandler('reportAction', 'ignore')
  async handleIgnoreReport(ctx: ComponentContext): Promise<void> {
    const [reportId] = ctx.customId.args;

    try {
      // Update report status in database
      await db.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.IGNORED,
          handledBy: ctx.user.id,
          handledAt: new Date(),
        },
      });

      // Update UI
      await this.updateButtonComponents(ctx, reportId, ReportStatus.IGNORED, 'Ignored');

      // Notify reporter
      await this.notifyReporter(ctx, reportId, ReportStatus.IGNORED);
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: `Failed to ignore report ${reportId}`,
      });
    }
  }

  @RegisterInteractionHandler('reportAction', 'escalate')
  async handleEscalateReport(ctx: ComponentContext): Promise<void> {
    const [reportId] = ctx.customId.args;

    try {
      // Update report status in database
      await db.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.ESCALATED,
          handledBy: ctx.user.id,
          handledAt: new Date(),
        },
      });

      // Update UI
      await this.updateButtonComponents(ctx, reportId, ReportStatus.ESCALATED, 'Escalated');

      // Notify reporter
      await this.notifyReporter(ctx, reportId, ReportStatus.ESCALATED);
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: `Failed to escalate report ${reportId}`,
      });
    }
  }

  @RegisterInteractionHandler('reply_context_view')
  async handleViewContext(ctx: ComponentContext): Promise<void> {
    try {
      await ctx.deferReply({ flags: ['Ephemeral'] });

      // Extract message ID from the report log to find the reply context
      const message = ctx.interaction.message;
      if (!message) {
        await ctx.editReply({ content: '❌ Could not find the original message.' });
        return;
      }

      // Parse the message content to extract the original message ID
      // This is a simplified approach - in a real implementation, you might store this data differently
      const content = message.content || '';
      const messageIdMatch = content.match(/Message ID.*?`([^`]+)`/);

      if (!messageIdMatch) {
        await ctx.editReply({ content: '❌ Could not find the message ID in the report.' });
        return;
      }

      const messageId = messageIdMatch[1];

      // Find the original message and its reply context
      const originalMessage = await db.message.findUnique({
        where: { id: messageId },
        include: {
          referredTo: true,
        },
      });

      if (!originalMessage?.referredTo) {
        await ctx.editReply({ content: '❌ No reply context found for this message.' });
        return;
      }

      // Get the author of the original message
      const authorId = originalMessage.referredTo.authorId;
      const originalAuthor = await ctx.client.users.fetch(authorId).catch(() => null);

      const embed = {
        title: '💬 Reply Context',
        description: `**Original Message Content:**\n\`\`\`\n${originalMessage.referredTo.content || 'No content'}\n\`\`\``,
        fields: [
          {
            name: '👤 Original Author',
            value: originalAuthor
              ? `${originalAuthor.username} (\`${originalAuthor.id}\`)`
              : `Unknown User (\`${originalMessage.referredTo.authorId}\`)`,
            inline: true,
          },
          {
            name: '📅 Sent At',
            value: `<t:${Math.floor(originalMessage.referredTo.createdAt.getTime() / 1000)}:F>`,
            inline: true,
          },
          {
            name: '🆔 Message ID',
            value: `\`${originalMessage.referredTo.id}\``,
            inline: true,
          },
        ],
        color: 0x5865f2,
        timestamp: new Date().toISOString(),
      };

      await ctx.editReply({ embeds: [embed] });
    }
    catch (error) {
      handleError(error, {
        repliable: ctx.interaction,
        comment: 'Failed to view reply context',
      });
    }
  }
}
